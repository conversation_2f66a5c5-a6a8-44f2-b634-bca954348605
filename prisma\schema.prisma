// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  password     String
  role         UserRole @default(CUSTOMER)
  fullName     String   @map("full_name")
  phoneNumber  String   @map("phone_number")
  address      String
  status       UserStatus @default(INACTIVE)

  // Affiliate System Fields
  referralCode     String?   @unique @map("referral_code")
  referredBy       String?   @map("referred_by")
  affiliateLevel   Int       @default(1) @map("affiliate_level") // Cấp độ affiliate (1-5)
  totalSales       Decimal   @default(0) @db.Decimal(12, 2) @map("total_sales")
  totalCommission  Decimal   @default(0) @db.Decimal(12, 2) @map("total_commission")
  availableBalance Decimal   @default(0) @db.Decimal(12, 2) @map("available_balance") // <PERSON><PERSON> tiền có thể rút
  totalWithdrawn   Decimal   @default(0) @db.Decimal(12, 2) @map("total_withdrawn")
  commissionRate   Decimal   @default(0) @db.Decimal(5, 4) @map("commission_rate") // Tỷ lệ hoa hồng (0.0000-1.0000)

  // Timestamps
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  // Relations
  idCards          IdCard[]
  bankAccounts     BankAccount[]
  orders           Order[]
  posts            Post[]
    // Affiliate Relations
  referredByUser   User?    @relation("UserReferral", fields: [referredBy], references: [id])
  referredUsers    User[]   @relation("UserReferral")
  commissions      Commission[]
  commissionEarned Commission[] @relation("UserCommissions")
  withdrawals      Withdrawal[]

  // Payment Relations
  paymentMethods   UserPaymentMethod[]

  // Cart Relations
  cartItems        CartItem[]

  // Affiliate Relations
  affiliateLinks   AffiliateLink[]

  @@map("users")
}

model IdCard {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  idCardNumber String   @map("id_card_number")
  frontImage   String   @map("front_image")
  backImage    String   @map("back_image")
  verifiedAt   DateTime? @map("verified_at") // null = chưa duyệt, có giá trị = đã duyệt
  rejectedAt   DateTime? @map("rejected_at") // null = chưa từ chối, có giá trị = đã từ chối
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("id_cards")
}

model BankAccount {
  id            String   @id @default(cuid())
  userId        String   @map("user_id")
  bankName      BankName @map("bank_name")
  accountNumber String   @map("account_number")
  branch        String
  accountName   String   @map("account_name")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  withdrawals Withdrawal[]

  @@map("bank_accounts")
}



model Category {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  parentId    String?  @map("parent_id")
  status      CategoryStatus @default(ACTIVE)
  sortOrder   Int      @default(0) @map("sort_order")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  parent   Category?  @relation("CategoryToCategory", fields: [parentId], references: [id])
  children Category[] @relation("CategoryToCategory")
  products Product[]
  affiliateLinks AffiliateLink[]

  @@map("categories")
}

model Product {
  id              String   @id @default(cuid())
  name            String
  slug            String   @unique
  description     String?
  content         String?
  price           Decimal  @db.Decimal(10, 2)
  salePrice       Decimal? @db.Decimal(10, 2) @map("sale_price")
  sku             String   @unique
  stock           Int      @default(0)
  images          Json?
  categoryId      String   @map("category_id")
  status          ProductStatus @default(ACTIVE)
  isFeatured      Boolean  @default(false) @map("is_featured")
  // NEW: Commission settings per product
  commissionRate  Decimal  @default(0) @db.Decimal(5, 4) @map("commission_rate") // Commission rate for this product
  allowAffiliate  Boolean  @default(true) @map("allow_affiliate") // Whether this product allows affiliate
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  category       Category @relation(fields: [categoryId], references: [id])
  orderItems     OrderItem[]
  cartItems      CartItem[]
  affiliateLinks AffiliateLink[]
  commissions    Commission[] // NEW: Commissions for this product

  @@map("products")
}

model Order {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  orderNumber  String   @unique @map("order_number")
  status       OrderStatus @default(PENDING)
  totalAmount  Decimal  @db.Decimal(10, 2) @map("total_amount")
  shippingFee  Decimal  @default(0) @db.Decimal(10, 2) @map("shipping_fee")
  discountAmount Decimal @default(0) @db.Decimal(10, 2) @map("discount_amount")
  paymentMethod PaymentMethod @map("payment_method")
  paymentStatus PaymentStatus @default(PENDING) @map("payment_status")
  shippingAddress Json @map("shipping_address")
  notes        String?
  trackingNumber String? @map("tracking_number")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  // Relations
  user       User @relation(fields: [userId], references: [id])
  orderItems OrderItem[]
  commissions Commission[]
  payments   Payment[]

  @@map("orders")
}

model OrderItem {
  id        String   @id @default(cuid())
  orderId   String   @map("order_id")
  productId String   @map("product_id")
  quantity  Int
  price     Decimal  @db.Decimal(10, 2)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  order       Order        @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product     Product      @relation(fields: [productId], references: [id])
  commissions Commission[] // NEW: Commissions for this order item

  @@map("order_items")
}

model PostCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  status      CategoryStatus @default(ACTIVE)
  sortOrder   Int      @default(0) @map("sort_order")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  posts Post[]

  @@map("post_categories")
}

model Post {
  id          String   @id @default(cuid())
  title       String
  slug        String   @unique
  content     String   @db.Text // Cho phép content dài
  excerpt     String?  @db.Text // Cho phép excerpt dài
  image       String?
  status      PostStatus @default(DRAFT)
  authorName  String   @map("author_name") // Tên tác giả nhập tay
  categoryId  String?  @map("category_id") // Danh mục bài viết
  authorId    String?  @map("author_id") // Optional relation với User
  publishedAt DateTime? @map("published_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  category PostCategory? @relation(fields: [categoryId], references: [id])
  author   User?         @relation(fields: [authorId], references: [id])

  @@map("posts")
}

model AffiliateLink {
  id               String   @id @default(cuid())
  userId           String   @map("user_id")
  type             AffiliateLinkType @default(GENERAL)
  productId        String?  @map("product_id")
  categoryId       String?  @map("category_id")
  title            String
  description      String?
  slug             String   @unique
  status           AffiliateLinkStatus @default(ACTIVE)
  commissionRate   Float    @default(5.0) @map("commission_rate")
  totalClicks      Int      @default(0) @map("total_clicks")
  totalConversions Int      @default(0) @map("total_conversions")
  totalCommission  Float    @default(0) @map("total_commission")
  lastClickAt      DateTime? @map("last_click_at")
  lastConversionAt DateTime? @map("last_conversion_at")
  expiresAt        DateTime? @map("expires_at")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  user        User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product     Product?                @relation(fields: [productId], references: [id], onDelete: Cascade)
  category    Category?               @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  clicks      AffiliateClick[]
  conversions AffiliateConversion[]
  commissions Commission[]            // NEW: Commissions generated by this link

  @@map("affiliate_links")
}

model AffiliateClick {
  id              String   @id @default(cuid())
  affiliateLinkId String   @map("affiliate_link_id")
  ipAddress       String   @map("ip_address")
  userAgent       String   @map("user_agent")
  referer         String?
  clickedAt       DateTime @default(now()) @map("clicked_at")

  // Relations
  affiliateLink AffiliateLink @relation(fields: [affiliateLinkId], references: [id], onDelete: Cascade)

  @@map("affiliate_clicks")
}

model AffiliateConversion {
  id              String   @id @default(cuid())
  affiliateLinkId String   @map("affiliate_link_id")
  orderId         String   @map("order_id")
  orderValue      Float    @map("order_value")
  commissionRate  Float    @map("commission_rate")
  commissionAmount Float   @map("commission_amount")
  convertedAt     DateTime @default(now()) @map("converted_at")

  // Relations
  affiliateLink AffiliateLink @relation(fields: [affiliateLinkId], references: [id], onDelete: Cascade)

  @@unique([affiliateLinkId, orderId])
  @@map("affiliate_conversions")
}

model Commission {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  orderId         String   @map("order_id")
  orderItemId     String?  @map("order_item_id") // NEW: Specific order item
  productId       String?  @map("product_id")    // NEW: Product that generated commission
  affiliateLinkId String?  @map("affiliate_link_id") // NEW: Affiliate link used
  referredUserId  String   @map("referred_user_id") // User được giới thiệu tạo đơn hàng
  level           Int      // Cấp độ commission (1=trực tiếp, 2=cấp 2, v.v.)
  commissionType  CommissionType @default(DIRECT) @map("commission_type") // NEW: Type of commission
  // Product-specific fields
  productQuantity Int?     @map("product_quantity") // NEW: Quantity of product
  productPrice    Decimal? @db.Decimal(12, 2) @map("product_price") // NEW: Price per unit
  orderAmount     Decimal  @db.Decimal(12, 2) @map("order_amount") // Total amount for this commission
  commissionRate  Decimal  @db.Decimal(5, 4) @map("commission_rate")
  amount          Decimal  @db.Decimal(12, 2) // Commission amount
  status          CommissionStatus @default(PENDING)
  paidAt          DateTime? @map("paid_at")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  user         User @relation(fields: [userId], references: [id])
  order        Order @relation(fields: [orderId], references: [id])
  orderItem    OrderItem? @relation(fields: [orderItemId], references: [id]) // NEW
  product      Product? @relation(fields: [productId], references: [id])     // NEW
  affiliateLink AffiliateLink? @relation(fields: [affiliateLinkId], references: [id]) // NEW
  referredUser User @relation("UserCommissions", fields: [referredUserId], references: [id])

  @@map("commissions")
}

model Withdrawal {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  amount          Decimal  @db.Decimal(12, 2)
  bankAccountId   String   @map("bank_account_id")
  status          WithdrawalStatus @default(PENDING)
  requestedAt     DateTime @default(now()) @map("requested_at")
  processedAt     DateTime? @map("processed_at")
  adminNote       String?  @map("admin_note")
  userNote        String?  @map("user_note")
  transactionId   String?  @map("transaction_id")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  user        User @relation(fields: [userId], references: [id])
  bankAccount BankAccount @relation(fields: [bankAccountId], references: [id])

  @@map("withdrawals")
}

// Payment System Models
model UserPaymentMethod {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  type        PaymentType
  isDefault   Boolean     @default(false) @map("is_default")
  isActive    Boolean     @default(true) @map("is_active")
  
  // MoMo specific fields
  momoPhone   String?     @map("momo_phone")
  momoName    String?     @map("momo_name")
  
  // Bank Transfer specific fields
  bankName    BankName?
  bankAccount String?     @map("bank_account")
  bankOwner   String?     @map("bank_owner")
  
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  
  // Relations
  user        User        @relation(fields: [userId], references: [id])
  
  @@map("user_payment_methods")
}

model Payment {
  id                String                    @id @default(cuid())
  orderId           String                    @map("order_id")
  amount            Decimal                   @db.Decimal(12, 2)
  type              PaymentType
  status            PaymentTransactionStatus @default(PENDING)
  
  // External payment gateway fields
  externalId        String?                   @map("external_id") // MoMo transaction ID, bank reference
  gatewayResponse   Json?                     @map("gateway_response") // Full response from payment gateway
  
  // MoMo specific fields
  momoTransId       String?                   @map("momo_trans_id")
  momoOrderId       String?                   @map("momo_order_id")
  momoRequestId     String?                   @map("momo_request_id")
  
  // Bank transfer specific fields
  bankTransferCode  String?                   @map("bank_transfer_code")
  bankReference     String?                   @map("bank_reference")
  
  // Timestamps
  paidAt            DateTime?                 @map("paid_at")
  expiredAt         DateTime?                 @map("expired_at")
  createdAt         DateTime                  @default(now()) @map("created_at")
  updatedAt         DateTime                  @updatedAt @map("updated_at")
  
  // Relations
  order             Order                     @relation(fields: [orderId], references: [id])
  
  @@map("payments")
}

// Enums
enum UserRole {
  CUSTOMER
  STAFF
  COLLABORATOR
  AGENT
  ADMIN
}

enum UserStatus {
  PENDING
  ACTIVE
  INACTIVE
}

// Removed IdCardStatus enum - using verifiedAt/rejectedAt timestamps instead

enum BankName {
  VIETCOMBANK
  VIETINBANK
  BIDV
  AGRIBANK
  TECHCOMBANK
  MBBANK
  TPBANK
}

enum CategoryStatus {
  ACTIVE
  INACTIVE
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPING
  DELIVERED
  CANCELLED
}

enum PaymentMethod {
  COD
  BANK_TRANSFER
  CREDIT_CARD
  E_WALLET
  MOMO
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PaymentType {
  MOMO
  BANK_TRANSFER
  CASH_ON_DELIVERY
  
  @@map("payment_type")
}

enum PaymentTransactionStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
  
  @@map("payment_transaction_status")
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum AffiliateLinkType {
  GENERAL
  PRODUCT
  CATEGORY
}

enum AffiliateLinkStatus {
  ACTIVE
  INACTIVE
  EXPIRED
}

enum CommissionStatus {
  PENDING
  PAID
  CANCELLED
}

enum CommissionType {
  DIRECT      // Hoa hồng trực tiếp từ affiliate link
  LEVEL       // Hoa hồng từ downline (10% của level dưới)
}

enum WithdrawalStatus {
  PENDING
  PROCESSING
  COMPLETED
  REJECTED
}

// FAQ System (keeping this as it's simple and useful)
model FAQ {
  id       String   @id @default(cuid())
  question String
  answer   String   @db.Text
  category String
  isActive Boolean  @default(true) @map("is_active")
  sortOrder Int     @default(0) @map("sort_order")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("faqs")
}

model CartItem {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  productId String   @map("product_id")
  quantity  Int
  price     Decimal  @db.Decimal(12, 2) // Giá tại thời điểm thêm vào giỏ
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("cart_items")
}

model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       Json
  description String?
  category    String   // commissions, registration, links, notifications, security
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("system_settings")
}

model CommissionLevelSetting {
  id              String   @id @default(cuid())
  level           Int      @unique // Level number (1, 2, 3...)
  commissionRate  Decimal  @db.Decimal(5, 4) @map("commission_rate") // Commission rate for this level
  description     String?  // Description of this level
  isActive        Boolean  @default(true) @map("is_active")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("commission_level_settings")
}

model ContactSection {
  id           String   @id @default(cuid())
  address      String
  phone        String
  email        String
  mapUrl       String?  @db.Text // Thay đổi từ String? sang String? @db.Text
  workingHours String?
  facebookUrl  String?
  twitterUrl   String?
  instagramUrl String?
  youtubeUrl   String?
  linkedinUrl  String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}




