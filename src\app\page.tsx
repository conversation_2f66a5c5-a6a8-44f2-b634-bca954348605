import { FC } from 'react';
import HeroSection from '@/components/home/<USER>';
import AboutSection from '@/components/home/<USER>';
import BenefitsSection from '@/components/home/<USER>';
import ProductCategories from '@/components/home/<USER>';
import FeaturedProducts from '@/components/home/<USER>';
import TestimonialsSection from '@/components/home/<USER>';
import ContactSection from '@/components/home/<USER>';
import { getHomePageData } from '@/lib/services/homepage';

const Home: FC = async () => {
  const homePageData = await getHomePageData();
  return (
    <>
      <div className="relative z-10">
        <main>
          <HeroSection data={homePageData.heroSection} />
          <AboutSection data={homePageData.aboutSection} />
          <BenefitsSection benefits={homePageData.benefits} />
          <ProductCategories categoriesData={homePageData.featuredCategories} />
          <FeaturedProducts productsData={homePageData.featuredProducts} />
          <TestimonialsSection data={homePageData.testimonials} />
          <ContactSection />
        </main>
      </div>
    </>
  );
};

export default Home;
