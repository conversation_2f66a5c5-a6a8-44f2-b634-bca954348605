{"name": "dongypharmacy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true next build", "prisma:generate": "prisma generate"}, "dependencies": {"@prisma/client": "^5.22.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-visually-hidden": "^1.2.0", "@tailwindcss/postcss": "^4.1.10", "@tinymce/tinymce-react": "^6.2.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.488.0", "next": "15.2.4", "next-auth": "^4.24.11", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.10", "zod": "^3.25.20"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9.25.0", "eslint-config-next": "^15.3.1", "prisma": "^5.22.0", "typescript": "5.8.3"}}