const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  const email = '<EMAIL>';
  const password = 'admin123'; // <PERSON><PERSON><PERSON> mật khẩu mạnh hơn khi dùng thật!
  const fullName = 'Admin';

  // Kiểm tra đã có admin chưa
  const existing = await prisma.user.findUnique({ where: { email } });
  if (existing) {
    console.log('Admin already exists!');
    return;
  }

  const hashedPassword = await bcrypt.hash(password, 10);

  const admin = await prisma.user.create({
    data: {
      email,
      password: hashedPassword,
      fullName,
      role: 'ADMIN',
      status: 'ACTIVE',
      isVerified: true,
    },
  });

  console.log('Admin created:', admin);
}

main()
  .catch(e => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());